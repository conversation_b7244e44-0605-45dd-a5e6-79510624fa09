#include <iostream>
#include <vector>
#include <cstdlib>
#include <ctime>
#include <algorithm>

// For proper Chinese display in Windows terminal
#include <windows.h> 

void drawLottery(int count_to_draw, int total_numbers = 30) {
    if (count_to_draw > total_numbers || count_to_draw <= 0) {
        std::cout << "Input error: count must be > 0 and <= total numbers." << std::endl;
        return;
    }

    std::vector<int> winning_numbers;

    // Use (size_t) for type conversion to eliminate -Wsign-compare warning
    while (winning_numbers.size() < (size_t)count_to_draw) {
        int random_number = rand() % total_numbers + 1;
        if (std::find(winning_numbers.begin(), winning_numbers.end(), random_number) == winning_numbers.end()) {
            winning_numbers.push_back(random_number);
        }
    }

    std::cout << "Lottery results:" << std::endl;
    // Use size_t i = 0 to eliminate -Wsign-compare warning in loop
    for (size_t i = 0; i < winning_numbers.size(); ++i) {
        std::cout << "Winner #" << i + 1 << ": " << winning_numbers[i] << std::endl;
    }
}

int main() {
    // Set terminal output to UTF-8 encoding to ensure Chinese characters display correctly
    SetConsoleOutputCP(65001); 

    // time(0) returns time_t, srand needs unsigned int, explicit conversion to eliminate -Wconversion warning
    srand((unsigned int)time(0));

    std::cout << "Starting lottery, drawing 10 numbers from 1-30..." << std::endl;
    drawLottery(10); 

    // Pause before program exit for easy result viewing
    system("pause");
    return 0;
}
