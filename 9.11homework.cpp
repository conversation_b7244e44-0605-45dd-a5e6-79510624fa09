#include <iostream>
#include <vector>
#include <cstdlib>
#include <ctime>
#include <algorithm>

// 为了在Windows终端正确显示中文，需要这个头文件
#include <windows.h> 

void drawLottery(int count_to_draw, int total_numbers = 30) {
    if (count_to_draw > total_numbers || count_to_draw <= 0) {
        std::cout << "输入错误：要抽取的数量必须大于0且不能超过总数量。" << std::endl;
        return;
    }

    std::vector<int> winning_numbers;

    // 使用 (size_t) 进行类型转换，可以消除 -Wsign-compare 警告
    while (winning_numbers.size() < (size_t)count_to_draw) {
        int random_number = rand() % total_numbers + 1;
        if (std::find(winning_numbers.begin(), winning_numbers.end(), random_number) == winning_numbers.end()) {
            winning_numbers.push_back(random_number);
        }
    }

    std::cout << "抽奖结果如下：" << std::endl;
    // 使用 size_t i = 0，可以消除循环中的 -Wsign-compare 警告
    for (size_t i = 0; i < winning_numbers.size(); ++i) {
        std::cout << "第 " << i + 1 << " 个中奖号码是: " << winning_numbers[i] << std::endl;
    }
}

int main() {
    // 设置终端输出为UTF-8编码，确保中文不乱码
    SetConsoleOutputCP(65001); 

    // time(0)返回time_t，srand需要unsigned int，进行一次显式转换消除 -Wconversion 警告
    srand((unsigned int)time(0));

    std::cout << "开始抽奖，将从 1~30 中抽取 10 个号码..." << std::endl;
    drawLottery(10); 

    // 在程序退出前暂停，方便查看结果
    system("pause");
    return 0;
}